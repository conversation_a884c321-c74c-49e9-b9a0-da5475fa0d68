<?php
// توابع مدیریت ووچر

/**
 * بررسی وجود ووچر و اعتبارسنجی
 */
function validateVoucher($voucher_code) {
    $voucherFile = "data/vouchers/{$voucher_code}.json";
    
    if (!file_exists($voucherFile)) {
        return [
            'valid' => false,
            'message' => 'کد ووچر وجود ندارد.'
        ];
    }
    
    $voucherData = json_decode(file_get_contents($voucherFile), true);
    
    if ($voucherData['is_used']) {
        return [
            'valid' => false,
            'message' => 'این کد ووچر قبلاً استفاده شده است.',
            'data' => $voucherData
        ];
    }
    
    if ($voucherData['status'] !== 'active') {
        return [
            'valid' => false,
            'message' => 'این کد ووچر غیرفعال است.',
            'data' => $voucherData
        ];
    }
    
    return [
        'valid' => true,
        'message' => 'کد ووچر معتبر است.',
        'data' => $voucherData
    ];
}

/**
 * بررسی مالکیت ووچر
 */
function checkVoucherOwnership($voucher_code, $user_id) {
    $validation = validateVoucher($voucher_code);
    
    if (!$validation['valid']) {
        return $validation;
    }
    
    $voucherData = $validation['data'];
    
    if ($voucherData['creator_user_id'] != $user_id) {
        return [
            'valid' => false,
            'message' => 'شما مالک این کد ووچر نیستید.',
            'data' => $voucherData
        ];
    }
    
    return [
        'valid' => true,
        'message' => 'شما مالک این کد ووچر هستید.',
        'data' => $voucherData
    ];
}

/**
 * فروش ووچر و اضافه کردن به کیف پول
 */
function sellVoucher($voucher_code, $user_id) {
    $ownership = checkVoucherOwnership($voucher_code, $user_id);
    
    if (!$ownership['valid']) {
        return $ownership;
    }
    
    $voucherData = $ownership['data'];
    $voucher_value = $voucherData['amount_toman'];
    
    // اضافه کردن به کیف پول کاربر
    $result = addToWallet($user_id, $voucher_value);
    
    if (!$result['success']) {
        return [
            'valid' => false,
            'message' => 'خطا در اضافه کردن به کیف پول.',
            'data' => $voucherData
        ];
    }
    
    // علامت‌گذاری ووچر به عنوان استفاده شده
    $voucherData['is_used'] = true;
    $voucherData['used_by'] = $user_id;
    $voucherData['used_at'] = date('Y-m-d H:i:s');
    $voucherData['status'] = 'sold';
    
    // ذخیره تغییرات ووچر
    $voucherFile = "data/vouchers/{$voucher_code}.json";
    file_put_contents($voucherFile, json_encode($voucherData, JSON_PRETTY_PRINT));
    
    // ثبت تراکنش
    logTransaction($user_id, 'voucher_sell', $voucher_value, $voucher_code);
    
    return [
        'valid' => true,
        'message' => 'ووچر با موفقیت فروخته شد.',
        'data' => $voucherData,
        'amount' => $voucher_value,
        'new_balance' => $result['new_balance']
    ];
}

/**
 * اضافه کردن مبلغ به کیف پول کاربر
 */
function addToWallet($user_id, $amount) {
    $userFile = "data/user/{$user_id}.json";
    
    if (!file_exists($userFile)) {
        return [
            'success' => false,
            'message' => 'فایل کاربری یافت نشد.'
        ];
    }
    
    $userData = json_decode(file_get_contents($userFile), true);
    
    // اگر کیف پول وجود ندارد، ایجاد کن
    if (!isset($userData['userfild']['wallet'])) {
        $userData['userfild']['wallet'] = [
            'balance' => 0,
            'transactions' => []
        ];
    }
    
    // اضافه کردن مبلغ
    $old_balance = $userData['userfild']['wallet']['balance'];
    $new_balance = $old_balance + $amount;
    $userData['userfild']['wallet']['balance'] = $new_balance;
    
    // ثبت تراکنش در کیف پول
    $userData['userfild']['wallet']['transactions'][] = [
        'type' => 'voucher_sell',
        'amount' => $amount,
        'old_balance' => $old_balance,
        'new_balance' => $new_balance,
        'date' => date('Y-m-d H:i:s'),
        'timestamp' => time()
    ];
    
    // ذخیره تغییرات
    file_put_contents($userFile, json_encode($userData, JSON_PRETTY_PRINT));
    
    return [
        'success' => true,
        'old_balance' => $old_balance,
        'new_balance' => $new_balance,
        'amount_added' => $amount
    ];
}

/**
 * دریافت موجودی کیف پول کاربر
 */
function getWalletBalance($user_id) {
    $userFile = "data/user/{$user_id}.json";
    
    if (!file_exists($userFile)) {
        return 0;
    }
    
    $userData = json_decode(file_get_contents($userFile), true);
    
    return isset($userData['userfild']['wallet']['balance']) ? 
           $userData['userfild']['wallet']['balance'] : 0;
}

/**
 * ثبت تراکنش در لاگ کلی
 */
function logTransaction($user_id, $type, $amount, $voucher_code = null) {
    // ایجاد پوشه transactions در صورت عدم وجود
    if (!file_exists("data/transactions")) {
        mkdir("data/transactions", 0777, true);
    }
    
    $transactionData = [
        'user_id' => $user_id,
        'type' => $type,
        'amount' => $amount,
        'voucher_code' => $voucher_code,
        'date' => date('Y-m-d H:i:s'),
        'timestamp' => time()
    ];
    
    // ذخیره در فایل تراکنش‌های روزانه
    $date = date('Y-m-d');
    $transactionFile = "data/transactions/transactions_{$date}.json";
    
    $transactions = [];
    if (file_exists($transactionFile)) {
        $transactions = json_decode(file_get_contents($transactionFile), true);
    }
    
    $transactions[] = $transactionData;
    file_put_contents($transactionFile, json_encode($transactions, JSON_PRETTY_PRINT));
}

/**
 * ارسال پیام تلگرام
 */
function sendTelegramMessage($chat_id, $text, $reply_markup = null) {
    $botToken = "7626326794:AAHKrt-VqqMbv72cJzbLIFMGQK-nE647fiE";
    $url = "https://api.telegram.org/bot{$botToken}/sendMessage";
    
    $data = [
        'chat_id' => $chat_id,
        'text' => $text,
        'parse_mode' => 'HTML'
    ];
    
    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    $response = curl_exec($ch);
    curl_close($ch);
    
    return json_decode($response);
}
?>
